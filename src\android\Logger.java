package cordova.plugin.unvired.logger;

import android.content.Context;
import android.os.Environment;

import org.apache.cordova.CordovaPlugin;
import org.apache.cordova.CallbackContext;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class Logger extends CordovaPlugin {

    private static final String LOG_FILE_NAME = "log.txt";
    private static final String BACKUP_LOG_FILE_NAME = "log_backup.txt";
    private static final long MAX_LOG_SIZE = 5 * 1024 * 1024; // 5MB

    private String defaultLogLevel = "important";

    @Override
    public boolean execute(String action, JSONArray args, final CallbackContext callbackContext) throws JSONException {
        try {
            switch (action) {
                case "logDebug":
                case "logInfo":
                case "logError":
                case "loggerWithLevel":
                    logWithLevel(args, callbackContext);
                    return true;
                case "setLogLevel":
                    setLogLevel(args, callbackContext);
                    return true;
                case "getLogLevel":
                    callbackContext.success(defaultLogLevel);
                    return true;
                case "getLogFileContent":
                    getLogFileContent(args, callbackContext);
                    return true;
                case "clearLogFile":
                    clearLogFile(args, callbackContext);
                    return true;
                case "getBackupLogFileContent":
                    getBackupLogFileContent(args, callbackContext);
                    return true;
                case "copyLogToBackup":
                    copyLogToBackup(args, callbackContext);
                    return true;
                case "getLogFileURL":
                    getLogFileURL(args, callbackContext);
                    return true;
                case "getBackupLogFileURL":
                    getBackupLogFileURL(args, callbackContext);
                    return true;
                default:
                    return false;
            }
        } catch (Exception e) {
            callbackContext.error("Error: " + e.getMessage());
            return false;
        }
    }

    private void setLogLevel(JSONArray args, CallbackContext callbackContext) {
        try {
            String level = args.getString(0);
            defaultLogLevel = level;
            callbackContext.success();
        } catch (Exception e) {
            callbackContext.error("Failed to set log level: " + e.getMessage());
        }
    }

    private void logWithLevel(JSONArray args, CallbackContext callbackContext) {
        try {
            JSONObject arg = args.getJSONObject(0);
            String userId = arg.optString("userId");
            String level = arg.optString("level");
            String sourceClass = arg.optString("sourceClass", "");
            String sourceMethod = arg.optString("sourceMethod", "");
            String message = arg.optString("message");

            if (userId.isEmpty() || level.isEmpty() || message.isEmpty()) {
                callbackContext.error("userId, level, and message are required");
                return;
            }

            // Log level filtering
            if (shouldSkipLog(level)) {
                callbackContext.success();
                return;
            }

            File logFile = getLogFile(userId);
            checkAndRotateLogFile(logFile, userId);

            String logEntry = formatLogEntry(level, sourceClass, sourceMethod, message);
            appendToFile(logFile, logEntry);

            callbackContext.success("Logged to " + logFile.getAbsolutePath());
        } catch (Exception e) {
            callbackContext.error("Logging failed: " + e.getMessage());
        }
    }

    private boolean shouldSkipLog(String level) {
        String normLevel = level.toLowerCase();
        String normDefault = defaultLogLevel.toLowerCase();
        if (normDefault.equals("error") && (normLevel.equals("debug") || normLevel.equals("important"))) return true;
        if (normDefault.equals("important") && normLevel.equals("debug")) return true;
        return false;
    }

    private void getLogFileContent(JSONArray args, CallbackContext callbackContext) {
        try {
            JSONObject arg = args.getJSONObject(0);
            String userId = arg.optString("userId");
            File logFile = getLogFile(userId);
            String content = readFile(logFile);
            callbackContext.success(content);
        } catch (Exception e) {
            callbackContext.error("Failed to read log file: " + e.getMessage());
        }
    }

    private void clearLogFile(JSONArray args, CallbackContext callbackContext) {
        try {
            JSONObject arg = args.getJSONObject(0);
            String userId = arg.optString("userId");
            File logFile = getLogFile(userId);
            new FileOutputStream(logFile).close();
            callbackContext.success();
        } catch (Exception e) {
            callbackContext.error("Failed to clear log file: " + e.getMessage());
        }
    }

    private void getBackupLogFileContent(JSONArray args, CallbackContext callbackContext) {
        try {
            JSONObject arg = args.getJSONObject(0);
            String userId = arg.optString("userId");
            File backupFile = getBackupLogFile(userId);
            String content = readFile(backupFile);
            callbackContext.success(content);
        } catch (Exception e) {
            callbackContext.error("Failed to read backup log file: " + e.getMessage());
        }
    }

    private void copyLogToBackup(JSONArray args, CallbackContext callbackContext) {
        try {
            JSONObject arg = args.getJSONObject(0);
            String userId = arg.optString("userId");
            File logFile = getLogFile(userId);
            File backupFile = getBackupLogFile(userId);
            copyFile(logFile, backupFile);
            callbackContext.success();
        } catch (Exception e) {
            callbackContext.error("Failed to copy log file to backup: " + e.getMessage());
        }
    }

    private void getLogFileURL(JSONArray args, CallbackContext callbackContext) {
        try {
            JSONObject arg = args.getJSONObject(0);
            String userId = arg.optString("userId");
            File logFile = getLogFile(userId);
            callbackContext.success(logFile.getAbsolutePath());
        } catch (Exception e) {
            callbackContext.error("Failed to get log file URL: " + e.getMessage());
        }
    }

    private void getBackupLogFileURL(JSONArray args, CallbackContext callbackContext) {
        try {
            JSONObject arg = args.getJSONObject(0);
            String userId = arg.optString("userId");
            File backupFile = getBackupLogFile(userId);
            callbackContext.success(backupFile.getAbsolutePath());
        } catch (Exception e) {
            callbackContext.error("Failed to get backup log file URL: " + e.getMessage());
        }
    }

    // --- Helper methods ---

    private File getLogFile(String userId) {
        File dir = getUserLogDir(userId);
        if (!dir.exists()) dir.mkdirs();
        return new File(dir, LOG_FILE_NAME);
    }

    private File getBackupLogFile(String userId) {
        File dir = getUserLogDir(userId);
        if (!dir.exists()) dir.mkdirs();
        return new File(dir, BACKUP_LOG_FILE_NAME);
    }

    private File getUserLogDir(String userId) {
        Context ctx = cordova.getActivity().getApplicationContext();
        File userDir = new File(ctx.getFilesDir(), userId);
        if (!userDir.exists()) userDir.mkdirs();
        return userDir;
    }

    private void checkAndRotateLogFile(File logFile, String userId) throws IOException {
        if (logFile.exists() && logFile.length() > MAX_LOG_SIZE) {
            File backupFile = getBackupLogFile(userId);
            copyFile(logFile, backupFile);
            new FileOutputStream(logFile).close();
        }
    }

    private String formatLogEntry(String level, String sourceClass, String sourceMethod, String message) {
        SimpleDateFormat localFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss", Locale.getDefault());
        SimpleDateFormat utcFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss", Locale.US);
        utcFormat.setTimeZone(java.util.TimeZone.getTimeZone("UTC"));
        Date now = new Date();
        String local = localFormat.format(now);
        String utc = utcFormat.format(now);
        String prefix = level.equalsIgnoreCase("error") ? "⭕️⭕️⭕️ " : "";
        return prefix + local + " | UTC:" + utc + " | " + getStringFromLevel(level) + " | " + sourceClass + " | " + sourceMethod + " | " + message + "\n";
    }

    private String getStringFromLevel(String level) {
        switch (level.toLowerCase()) {
            case "important": return "IMPORTANT";
            case "error": return "ERROR";
            case "debug": return "DEBUG";
            default: return "";
        }
    }

    private void appendToFile(File file, String data) throws IOException {
        FileWriter fw = new FileWriter(file, true);
        fw.write(data);
        fw.close();
    }

    private String readFile(File file) throws IOException {
        if (!file.exists()) return "";
        StringBuilder sb = new StringBuilder();
        BufferedReader br = new BufferedReader(new FileReader(file));
        String line;
        while ((line = br.readLine()) != null) sb.append(line).append("\n");
        br.close();
        return sb.toString();
    }

    private void copyFile(File src, File dest) throws IOException {
        if (!src.exists()) return;
        FileInputStream in = new FileInputStream(src);
        FileOutputStream out = new FileOutputStream(dest);
        byte[] buf = new byte[1024];
        int len;
        while ((len = in.read(buf)) > 0) out.write(buf, 0, len);
        in.close();
        out.close();
    }
}
