# Cordova Plugin Unvired Logger

A comprehensive Cordova plugin that provides advanced logging functionality with log levels, file management, and user-specific log organization. Supports Android, iOS, Electron, and Browser platforms.

## Installation

```bash
cordova plugin add cordova-plugin-unvired-logger
```

## Usage

### Basic Logging

```javascript
// Info log
Logger.logInfo("user123", "AuthService", "login", "User login successful",
    function(success) {
        console.log("Log saved:", success);
    },
    function(error) {
        console.error("Logging failed:", error);
    }
);

// Debug log
Logger.logDebug("user123", "UserService", "getUserData", "Fetching user data from API",
    function(success) {
        console.log("Debug log saved:", success);
    },
    function(error) {
        console.error("Debug logging failed:", error);
    }
);

// Error log
Logger.logError("user123", "UserService", "getUserData", "Failed to fetch user data: Network error",
    function(success) {
        console.log("Error log saved:", success);
    },
    function(error) {
        console.error("Error logging failed:", error);
    }
);
```

### Advanced Logging with Level

```javascript
Logger.loggerWithLevel("user123", "debug", "MyClass", "myMethod", "This is a debug message",
    function(success) {
        console.log("Log saved:", success);
    },
    function(error) {
        console.error("Logging failed:", error);
    }
);
```

### Log Level Management

```javascript
// Set log level to control which messages are logged
Logger.setLogLevel("debug", // Logs all levels
    function(success) {
        console.log("Log level set to Debug");
    },
    function(error) {
        console.error("Failed to set log level:", error);
    }
);

Logger.setLogLevel("info", // Logs Info and Error only
    function(success) {
        console.log("Log level set to Info");
    },
    function(error) {
        console.error("Failed to set log level:", error);
    }
);

Logger.setLogLevel("error", // Logs Error only
    function(success) {
        console.log("Log level set to Error");
    },
    function(error) {
        console.error("Failed to set log level:", error);
    }
);
```

### Log File Management

```javascript
// Get log file content
Logger.getLogFileContent("user123",
    function(content) {
        console.log("Log file content:", content);
    },
    function(error) {
        console.error("Failed to read log file:", error);
    }
);

// Clear log file
Logger.clearLogFile("user123",
    function(success) {
        console.log("Log file cleared successfully");
    },
    function(error) {
        console.error("Failed to clear log file:", error);
    }
);

// Get backup log file content (when log rotation occurs)
Logger.getBackupLogFileContent("user123",
    function(content) {
        console.log("Backup log file content:", content);
    },
    function(error) {
        console.error("Failed to read backup log file:", error);
    }
);
```

### Complete Test Example

```javascript
function testLogger() {
    if (window && Logger) {
        console.log("Testing Logger plugin...");
        
        // Test basic logging
        Logger.logInfo("testuser", "AuthService", "login", "User login successful",
            function(success) {
                console.log("Basic log success:", success);
                
                // Test log level setting
                Logger.setLogLevel("debug",
                    function() {
                        console.log("Log level set to Debug");
                        
                        // Test convenience methods
                        Logger.logDebug("testuser", "TestClass", "testMethod", "Debug message test",
                            function() {
                                console.log("Debug log successful");
                                
                                // Test reading log content
                                Logger.getLogFileContent("testuser",
                                    function(content) {
                                        console.log("Log file content:", content);
                                    },
                                    function(error) {
                                        console.error("Failed to read log:", error);
                                    }
                                );
                            },
                            function(error) {
                                console.error("Debug log failed:", error);
                            }
                        );
                    },
                    function(error) {
                        console.error("Failed to set log level:", error);
                    }
                );
            },
            function(error) {
                console.error("Basic log failed:", error);
            }
        );
    } else {
        console.warn("Logger not available.");
    }
}

// Call the test function
testLogger();
```

## Supported Platforms

- **Android**: Native Java implementation with file system access
- **iOS**: Native Swift implementation with file system access
- **Electron**: Node.js implementation with file system access
- **Browser**: JavaScript implementation using localStorage

## Features

- **Advanced logging**: Log messages with class, method, and log level information
- **Log levels**: Debug, Info, and Error levels with filtering
- **User-specific logging**: Logs are organized by user ID
- **Automatic file creation**: Log files and directories are created automatically
- **Log rotation**: Automatic backup when log file exceeds 5MB
- **Timestamped entries**: Each log entry includes local and UTC timestamps
- **File management**: Read, clear, and backup log files
- **Cross-platform**: Consistent API across all supported platforms
- **Convenience methods**: Easy-to-use methods for different log levels

## Platform-Specific Details

### Android
- Logs are stored in the app's internal storage: `/data/data/[package]/files/[userId]/`
- Requires storage permissions for external storage access
- Automatic log rotation when file size exceeds 5MB

### iOS
- Logs are stored in the app's Documents directory: `Documents/[userId]/`
- Uses native Swift implementation for optimal performance
- Automatic log rotation when file size exceeds 5MB

### Electron
- Logs are stored in the app's user data directory: `[userData]/[userId]/log/`
- Uses Node.js file system APIs
- Automatic log rotation when file size exceeds 5MB

### Browser
- Logs are stored in browser's localStorage with keys: `unvired_logger_[userId]_log.txt`
- Fallback implementation when Cordova is not available
- Automatic content truncation when size exceeds 5MB
- Console logging for debugging purposes

## Log Levels

- **Debug**: Detailed information for debugging (only logged when level is set to Debug)
- **Info**: General information about application flow (logged when level is Info or Debug)
- **Error**: Error messages and exceptions (always logged)

## File Structure

Logs are stored in the following structure:
```
{userDataPath}/{userId}/log.txt
{userDataPath}/{userId}/log_backup.txt
```

Where:
- `userDataPath` is the Electron app's user data directory
- `userId` is the log folder name
- `log.txt` is the current log file
- `log_backup.txt` is the backup file (created during rotation)

## API Reference

### Core Methods
- `log(userId, message, sourceClass?, sourceMethod?, level?, success, error)` - Log a message
- `setLogLevel(level, success, error)` - Set the logging level
- `getLogFileContent(userId, success, error)` - Get log file content
- `clearLogFile(userId, success, error)` - Clear the log file
- `getBackupLogFileContent(userId, success, error)` - Get backup log file content

### Convenience Methods
- `logDebug(userId, sourceClass, sourceMethod, message, success, error)` - Log debug message
- `logInfo(userId, sourceClass, sourceMethod, message, success, error)` - Log info message
- `logError(userId, sourceClass, sourceMethod, message, success, error)` - Log error message

### Constants
- `Logger.LogLevel.Debug` - Debug log level
- `Logger.LogLevel.Info` - Info log level
- `Logger.LogLevel.Error` - Error log level

## Requirements

- Cordova 9.0.0 or higher
- cordova-electron 1.0.0 or higher
- Electron 20.0.0 or higher
- Node.js 14.0.0 or higher

## License

MIT 