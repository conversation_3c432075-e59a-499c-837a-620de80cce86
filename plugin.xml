<?xml version="1.0" encoding="UTF-8"?>
<plugin id="cordova-plugin-unvired-logger" version="@PACKAGE_NUMBER@"
        xmlns="http://apache.org/cordova/ns/plugins/1.0"
        xmlns:android="http://schemas.android.com/apk/res/android">

    <name>Logger</name>
    <description>A logger plugin for Android, iOS, Electron, and Browser that appends logs to log.txt files organized by user ID.</description>
    
    <js-module src="www/logger.js" name="Logger">
        <clobbers target="Logger" />
    </js-module>

    <platform name="electron">
        <framework src="src/electron"/>
    </platform>

    <platform name="android">
        <config-file target="res/xml/config.xml" parent="/*">
            <feature name="Logger">
                <param name="android-package" value="cordova.plugin.unvired.logger.Logger" />
            </feature>
        </config-file>
        
        <source-file src="src/android/Logger.java" target-dir="src/cordova/plugin/unvired/logger" />
        
        <uses-permission android:name="android.permission.READ_PHONE_STATE" />
        <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
        <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    </platform>

    <platform name="ios">
        <config-file target="config.xml" parent="/*">
            <feature name="Logger">
                <param name="ios-package" value="Logger" />
            </feature>
        </config-file>
        
        <source-file src="src/ios/Logger.swift" />
        
        <framework src="Foundation.framework" />
        <framework src="UIKit.framework" />
    </platform>

    <platform name="browser">
        <js-module src="src/browser/Logger.js" name="LoggerProxy">
            <runs />
        </js-module>
    </platform>
</plugin>
