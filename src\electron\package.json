{"name": "cordova-plugin-unvired-logger", "version": "@PACKAGE_NUMBER@", "description": "Electron platform package for cordova-plugin-unvired-logger", "main": "Logger.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["electron", "<PERSON><PERSON>", "plugin", "logger"], "cordova": {"serviceName": "<PERSON><PERSON>"}, "author": "Unvired Inc", "license": "MIT", "dependencies": {"electron": ">=20.0.0"}, "devDependencies": {}, "engines": {"node": ">=14.0.0", "electron": ">=20.0.0"}, "peerDependencies": {"electron": ">=20.0.0"}}