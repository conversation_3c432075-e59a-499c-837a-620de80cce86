import Foundation
import UIKit

@objc(Logger) class Logger : CDVPlugin {
    
    private let LOG_FILE_NAME = "log.txt"
    private let BACKUP_LOG_FILE_NAME = "log_backup.txt"
    private let MAX_LOG_SIZE: Int64 = 5 * 1024 * 1024 // 5MB
    
    private var defaultLogLevel: String = "important"
    
    @objc(logDebug:)
    func logDebug(_ command: CDVInvokedUrlCommand) {
        loggerWithLevel(command)
    }
    
    @objc(logInfo:)
    func logInfo(_ command: CDVInvokedUrlCommand) {
        loggerWithLevel(command)
    }
    
    @objc(logError:)
    func logError(_ command: CDVInvokedUrlCommand) {
        loggerWithLevel(command)
    }
    
    @objc(loggerWithLevel:)
    func loggerWithLevel(_ command: CDVInvokedUrlCommand) {
        guard let args = command.arguments.first as? [String: Any] else {
            sendErrorResult(command.callbackId, message: "Invalid arguments")
            return
        }
        
        guard let userId = args["userId"] as? String, !userId.isEmpty else {
            sendErrorResult(command.callbackId, message: "userId is required")
            return
        }
        
        guard let level = args["level"] as? String, !level.isEmpty else {
            sendErrorResult(command.callbackId, message: "level is required")
            return
        }
        
        guard let message = args["message"] as? String, !message.isEmpty else {
            sendErrorResult(command.callbackId, message: "message is required")
            return
        }
        
        let sourceClass = args["sourceClass"] as? String ?? ""
        let sourceMethod = args["sourceMethod"] as? String ?? ""
        
        // Log level filtering
        if shouldSkipLog(level: level) {
            sendSuccessResult(command.callbackId, message: "Log skipped due to level filter")
            return
        }
        
        do {
            let logFile = try getLogFile(userId: userId)
            try checkAndRotateLogFile(logFile: logFile, userId: userId)
            
            let logEntry = formatLogEntry(level: level, sourceClass: sourceClass, sourceMethod: sourceMethod, message: message)
            try appendToFile(file: logFile, data: logEntry)
            
            sendSuccessResult(command.callbackId, message: "Logged to \(logFile.path)")
        } catch {
            sendErrorResult(command.callbackId, message: "Logging failed: \(error.localizedDescription)")
        }
    }
    
    @objc(setLogLevel:)
    func setLogLevel(_ command: CDVInvokedUrlCommand) {
        guard let args = command.arguments.first as? [String: Any],
              let level = args["level"] as? String else {
            sendErrorResult(command.callbackId, message: "Invalid log level")
            return
        }
        
        defaultLogLevel = level
        sendSuccessResult(command.callbackId, message: "Log level set to \(level)")
    }
    
    @objc(getLogLevel:)
    func getLogLevel(_ command: CDVInvokedUrlCommand) {
        sendSuccessResult(command.callbackId, message: defaultLogLevel)
    }
    
    @objc(getLogFileContent:)
    func getLogFileContent(_ command: CDVInvokedUrlCommand) {
        guard let args = command.arguments.first as? [String: Any],
              let userId = args["userId"] as? String else {
            sendErrorResult(command.callbackId, message: "userId is required")
            return
        }
        
        do {
            let logFile = try getLogFile(userId: userId)
            let content = try readFile(file: logFile)
            sendSuccessResult(command.callbackId, message: content)
        } catch {
            sendErrorResult(command.callbackId, message: "Failed to read log file: \(error.localizedDescription)")
        }
    }
    
    @objc(clearLogFile:)
    func clearLogFile(_ command: CDVInvokedUrlCommand) {
        guard let args = command.arguments.first as? [String: Any],
              let userId = args["userId"] as? String else {
            sendErrorResult(command.callbackId, message: "userId is required")
            return
        }
        
        do {
            let logFile = try getLogFile(userId: userId)
            try "".write(to: logFile, atomically: true, encoding: .utf8)
            sendSuccessResult(command.callbackId, message: "Log file cleared")
        } catch {
            sendErrorResult(command.callbackId, message: "Failed to clear log file: \(error.localizedDescription)")
        }
    }
    
    @objc(getBackupLogFileContent:)
    func getBackupLogFileContent(_ command: CDVInvokedUrlCommand) {
        guard let args = command.arguments.first as? [String: Any],
              let userId = args["userId"] as? String else {
            sendErrorResult(command.callbackId, message: "userId is required")
            return
        }
        
        do {
            let backupFile = try getBackupLogFile(userId: userId)
            let content = try readFile(file: backupFile)
            sendSuccessResult(command.callbackId, message: content)
        } catch {
            sendErrorResult(command.callbackId, message: "Failed to read backup log file: \(error.localizedDescription)")
        }
    }
    
    @objc(copyLogToBackup:)
    func copyLogToBackup(_ command: CDVInvokedUrlCommand) {
        guard let args = command.arguments.first as? [String: Any],
              let userId = args["userId"] as? String else {
            sendErrorResult(command.callbackId, message: "userId is required")
            return
        }
        
        do {
            let logFile = try getLogFile(userId: userId)
            let backupFile = try getBackupLogFile(userId: userId)
            try copyFile(from: logFile, to: backupFile)
            sendSuccessResult(command.callbackId, message: "Log file copied to backup")
        } catch {
            sendErrorResult(command.callbackId, message: "Failed to copy log file to backup: \(error.localizedDescription)")
        }
    }
    
    @objc(getLogFileURL:)
    func getLogFileURL(_ command: CDVInvokedUrlCommand) {
        guard let args = command.arguments.first as? [String: Any],
              let userId = args["userId"] as? String else {
            sendErrorResult(command.callbackId, message: "userId is required")
            return
        }
        
        do {
            let logFile = try getLogFile(userId: userId)
            sendSuccessResult(command.callbackId, message: logFile.path)
        } catch {
            sendErrorResult(command.callbackId, message: "Failed to get log file URL: \(error.localizedDescription)")
        }
    }
    
    @objc(getBackupLogFileURL:)
    func getBackupLogFileURL(_ command: CDVInvokedUrlCommand) {
        guard let args = command.arguments.first as? [String: Any],
              let userId = args["userId"] as? String else {
            sendErrorResult(command.callbackId, message: "userId is required")
            return
        }
        
        do {
            let backupFile = try getBackupLogFile(userId: userId)
            sendSuccessResult(command.callbackId, message: backupFile.path)
        } catch {
            sendErrorResult(command.callbackId, message: "Failed to get backup log file URL: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Helper Methods
    
    private func shouldSkipLog(level: String) -> Bool {
        let normLevel = level.lowercased()
        let normDefault = defaultLogLevel.lowercased()
        
        if normDefault == "error" && (normLevel == "debug" || normLevel == "important") {
            return true
        }
        if normDefault == "important" && normLevel == "debug" {
            return true
        }
        return false
    }
    
    private func getLogFile(userId: String) throws -> URL {
        let userDir = try getUserLogDir(userId: userId)
        return userDir.appendingPathComponent(LOG_FILE_NAME)
    }
    
    private func getBackupLogFile(userId: String) throws -> URL {
        let userDir = try getUserLogDir(userId: userId)
        return userDir.appendingPathComponent(BACKUP_LOG_FILE_NAME)
    }
    
    private func getUserLogDir(userId: String) throws -> URL {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let userDir = documentsPath.appendingPathComponent(userId)
        
        if !FileManager.default.fileExists(atPath: userDir.path) {
            try FileManager.default.createDirectory(at: userDir, withIntermediateDirectories: true, attributes: nil)
        }
        
        return userDir
    }
    
    private func checkAndRotateLogFile(logFile: URL, userId: String) throws {
        if FileManager.default.fileExists(atPath: logFile.path) {
            let attributes = try FileManager.default.attributesOfItem(atPath: logFile.path)
            if let fileSize = attributes[.size] as? Int64, fileSize > MAX_LOG_SIZE {
                let backupFile = try getBackupLogFile(userId: userId)
                try copyFile(from: logFile, to: backupFile)
                try "".write(to: logFile, atomically: true, encoding: .utf8)
            }
        }
    }
    
    private func formatLogEntry(level: String, sourceClass: String, sourceMethod: String, message: String) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd-MM-yyyy HH:mm:ss"
        dateFormatter.locale = Locale.current
        let localDate = dateFormatter.string(from: Date())
        
        let utcFormatter = DateFormatter()
        utcFormatter.dateFormat = "dd-MM-yyyy HH:mm:ss"
        utcFormatter.timeZone = TimeZone(abbreviation: "UTC")
        let utcDate = utcFormatter.string(from: Date())
        
        let prefix = level.lowercased() == "error" ? "⭕️⭕️⭕️ " : ""
        return "\(prefix)\(localDate) | UTC:\(utcDate) | \(getStringFromLevel(level: level)) | \(sourceClass) | \(sourceMethod) | \(message)\n"
    }
    
    private func getStringFromLevel(level: String) -> String {
        switch level.lowercased() {
        case "important": return "IMPORTANT"
        case "error": return "ERROR"
        case "debug": return "DEBUG"
        default: return ""
        }
    }
    
    private func appendToFile(file: URL, data: String) throws {
        let fileManager = FileManager.default
        if !fileManager.fileExists(atPath: file.path) {
            // Create the file if it doesn't exist
            fileManager.createFile(atPath: file.path, contents: nil, attributes: nil)
        }
        let fileHandle = try FileHandle(forWritingTo: file)
        fileHandle.seekToEndOfFile()
        fileHandle.write(data.data(using: .utf8)!)
        fileHandle.closeFile()
    }
    
    private func readFile(file: URL) throws -> String {
        if !FileManager.default.fileExists(atPath: file.path) {
            return ""
        }
        return try String(contentsOf: file, encoding: .utf8)
    }
    
    private func copyFile(from: URL, to: URL) throws {
        if !FileManager.default.fileExists(atPath: from.path) {
            return
        }
        try FileManager.default.copyItem(at: from, to: to)
    }
    
    private func sendSuccessResult(_ callbackId: String, message: String) {
        let result = CDVPluginResult(status: CDVCommandStatus_OK, messageAs: message)
        self.commandDelegate.send(result, callbackId: callbackId)
    }
    
    private func sendErrorResult(_ callbackId: String, message: String) {
        let result = CDVPluginResult(status: CDVCommandStatus_ERROR, messageAs: message)
        self.commandDelegate.send(result, callbackId: callbackId)
    }
} 