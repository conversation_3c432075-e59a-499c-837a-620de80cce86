const LOG_FILE_NAME = 'log.txt';
const BACKUP_LOG_FILE_NAME = 'log_backup.txt';
const MAX_LOG_SIZE = 5 * 1024 * 1024; // 5MB

const LogLevel = {
    Debug: 'debug',
    Error: 'error',
    Info: 'important',
};

let defaultLogLevel = LogLevel.Info;

// Browser storage keys
const getStorageKey = (userId, fileName) => `unvired_logger_${userId}_${fileName}`;
const getLogLevelKey = (userId) => `unvired_logger_${userId}_level`;

module.exports.logDebug = async function (successCallback, errorCallback, args) {
    await logWitLevel(successCallback, errorCallback, args);
}

module.exports.logError = async function (successCallback, errorCallback, args) {
    await logWitLevel(successCallback, errorCallback, args);
}

module.exports.logInfo = async function (successCallback, errorCallback, args) {
    await logWitLevel(successCallback, errorCallback, args);
}

module.exports.setLogLevel = function(successCallback, errorCallback, level) {
    // If level is an array (from Cordova), extract the first element
    let levelToSet = Array.isArray(level) ? level[0] : level;
    
    // Normalize the level to lowercase for consistency
    defaultLogLevel = (levelToSet || '').toLowerCase();
    
    successCallback("");
}

module.exports.getLogLevel = function(successCallback, errorCallback) {
    successCallback(defaultLogLevel)
}

module.exports.getLogFileURL = function(successCallback, errorCallback, args) {
    // In browser, we return a data URL or storage reference
    successCallback(`browser://${getStorageKey(args[0].userId, LOG_FILE_NAME)}`);
}

module.exports.getLogFileContent = async function (successCallback, errorCallback, args) {
    try {
        const userId = args[0].userId;
        const storageKey = getStorageKey(userId, LOG_FILE_NAME);
        const content = localStorage.getItem(storageKey) || '';
        successCallback(content);
    } catch (err) {
        errorCallback(new Error(`Failed to read log file: ${err.message}`));
    }
}

module.exports.clearLogFile = async function (successCallback, errorCallback, args) {
    try {
        const userId = args[0].userId;
        const storageKey = getStorageKey(userId, LOG_FILE_NAME);
        localStorage.removeItem(storageKey);
        successCallback();
    } catch (err) {
        errorCallback(new Error(`Failed to clear log file: ${err.message}`));
    }
}

module.exports.getBackupLogFileURL = function (successCallback, errorCallback, args) {
    successCallback(`browser://${getStorageKey(args[0].userId, BACKUP_LOG_FILE_NAME)}`);
}

module.exports.getBackupLogFileContent = async function(successCallback, errorCallback, args) {
    try {
        const userId = args[0].userId;
        const storageKey = getStorageKey(userId, BACKUP_LOG_FILE_NAME);
        const content = localStorage.getItem(storageKey) || '';
        successCallback(content);
    } catch (err) {
        errorCallback(new Error(`Failed to read backup log file: ${err.message}`));
    }
}

module.exports.copyLogToBackup = async function(successCallback, errorCallback, args) {
    try {
        const userId = args[0].userId;
        const logStorageKey = getStorageKey(userId, LOG_FILE_NAME);
        const backupStorageKey = getStorageKey(userId, BACKUP_LOG_FILE_NAME);
        const logContent = localStorage.getItem(logStorageKey) || '';
        localStorage.setItem(backupStorageKey, logContent);
        successCallback();
    } catch (err) {
        errorCallback(new Error(`Failed to copy log file to backup: ${err.message}`));
    }
}

module.exports.loggerWithLevel = async function(successCallback, errorCallback, args) {
    await logWitLevel(successCallback, errorCallback, args)
}

function getStringFromLevel(level) {
    switch (level) {
        case LogLevel.Info: return 'IMPORTANT';
        case LogLevel.Error: return 'ERROR';
        case LogLevel.Debug: return 'DEBUG';
        default: return '';
    }
}

async function logWitLevel(successCallback, errorCallback, args)  {
    try {
        if (!args || !args[0]) {
            return errorCallback(new Error("Invalid arguments provided"));
        }
        
        const { userId, level, sourceClass, sourceMethod, message } = args[0];
        
        if (!userId) return errorCallback(new Error("userId is required"));
        if (!level) return errorCallback(new Error("level is required"));
        if (!message) return errorCallback(new Error("message is required"));

        // Normalize log levels for comparison
        const normalizedDefaultLogLevel = (defaultLogLevel || '').toLowerCase();
        const normalizedLevel = (level || '').toLowerCase();

        // Log level filtering using normalized values
        if (
            (normalizedDefaultLogLevel === LogLevel.Error && (normalizedLevel === LogLevel.Debug || normalizedLevel === LogLevel.Info)) ||
            (normalizedDefaultLogLevel === LogLevel.Info && normalizedLevel === LogLevel.Debug)
        ) {
            return successCallback();
        }

        await checkAndRotateLogFile(args);

        const currentDate = new Date();
        const formatter = new Intl.DateTimeFormat('en-US', {
            day: '2-digit', month: '2-digit', year: 'numeric',
            hour: '2-digit', minute: '2-digit', second: '2-digit'
        });
        const localDateString = formatter.format(currentDate);

        const dateUtc = new Date(currentDate.toUTCString());
        const utcFormatter = new Intl.DateTimeFormat('en-US', {
            day: '2-digit', month: '2-digit', year: 'numeric',
            hour: '2-digit', minute: '2-digit', second: '2-digit', timeZone: 'UTC'
        });
        const utcDateString = utcFormatter.format(dateUtc);

        let data = `${localDateString} | UTC:${utcDateString} | ${getStringFromLevel(level)} | ${sourceClass} | ${sourceMethod} | ${message}\n`;
        if (level === LogLevel.Error) {
            data = `⭕️⭕️⭕️ ${data}`;
        }

        // Log to console for browser debugging
        console.log(data);

        // Store in localStorage
        const storageKey = getStorageKey(userId, LOG_FILE_NAME);
        const existingContent = localStorage.getItem(storageKey) || '';
        const newContent = existingContent + data;
        
        // Check size limit
        if (newContent.length > MAX_LOG_SIZE) {
            // If content is too large, truncate it
            const truncatedContent = newContent.substring(newContent.length - MAX_LOG_SIZE / 2);
            localStorage.setItem(storageKey, truncatedContent);
        } else {
            localStorage.setItem(storageKey, newContent);
        }

        successCallback(`Logged to browser storage: ${storageKey}`);
    } catch (err) {
        errorCallback(new Error(`Logging failed: ${err.message}`));
    }
}

async function checkAndRotateLogFile (args) {
    return new Promise((resolve, reject) => {
        try {
            const userId = args[0].userId;
            const storageKey = getStorageKey(userId, LOG_FILE_NAME);
            const content = localStorage.getItem(storageKey) || '';
            
            if (content.length > MAX_LOG_SIZE) {
                const backupStorageKey = getStorageKey(userId, BACKUP_LOG_FILE_NAME);
                localStorage.setItem(backupStorageKey, content);
                localStorage.setItem(storageKey, '');
            }
            resolve();
        } catch (err) {
            reject(new Error(`Failed to rotate log file: ${err.message}`));
        }
    })
}

require('cordova/exec/proxy').add('Logger', module.exports);