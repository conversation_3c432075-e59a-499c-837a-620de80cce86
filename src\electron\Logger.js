const { app } = require('electron');
const path = require('path');
const fs = require('fs');
const fsPromises = fs.promises;

// --- Proper async lock for log file operations ---
let logLockPromise = Promise.resolve();
let isLocked = false;

async function withLogLock(fn) {
    // Wait for any existing lock to complete
    await logLockPromise;
    
    // Create a new promise for this operation
    const operationPromise = (async () => {
        try {
            isLocked = true;
            const result = await fn();
            return result;
        } catch (error) {
            console.error('[Logger] Error in log operation:', error);
            // Return error message instead of throwing to prevent unhandled promise rejections
            return `Error in log operation: ${error.message}`;
        } finally {
            isLocked = false;
        }
    })();
    
    // Update the lock promise to wait for this operation
    logLockPromise = operationPromise;
    
    return operationPromise;
}

const LOG_FILE_NAME = 'log.txt';
const BACKUP_LOG_FILE_NAME = 'log_backup.txt';

const LogLevel = {
    Debug: 'debug',
    Error: 'error',
    Info: 'important',
};

let defaultLogLevel = LogLevel.Info;
const MAX_LOG_SIZE = 5 * 1024 * 1024; // 5MB

function logDebug(args) {
    return loggerWithLevel(args);
}

function logError(args) {
    return loggerWithLevel(args);
}

function logInfo(args) {
    return loggerWithLevel(args);
}

function setLogLevel(level) {
    // If level is an array (from Cordova), extract the first element
    let levelToSet = Array.isArray(level) ? level[0] : level;
    
    // Normalize the level to lowercase for consistency
    defaultLogLevel = (levelToSet || '').toLowerCase();
}

function getLogLevel() {
    return new Promise((resolve) => {
        resolve(defaultLogLevel);
    });
}

function getLogFileURL(args) {
    const userPath = getLogDirectory(args);
    return path.join(userPath, LOG_FILE_NAME);
}

function getLogFileContent(args) {
    return new Promise(async (resolve, reject) => {
        try {
            const logPath = getLogFileURL(args);
            if (fs.existsSync(logPath)) {
                const content = await fsPromises.readFile(logPath, 'utf8');
                resolve(content);
            } else {
                resolve('');
            }
        } catch (err) {
            reject(new Error(`Failed to read log file: ${err.message}`));
        }
    });
}

function clearLogFile(args) {
    return withLogLock(async () => {
        const logPath = getLogFileURL(args);
        await fsPromises.writeFile(logPath, '');
        return 'Log file cleared successfully';
    });
}

function getBackupLogFileURL(args) {
    const userPath = getLogDirectory(args);
    return path.join(userPath, BACKUP_LOG_FILE_NAME);
}

function getBackupLogFileContent(args) {
    return new Promise(async (resolve, reject) => {
        try {
            const backupPath = getBackupLogFileURL(args);
            if (fs.existsSync(backupPath)) {
                const content = await fsPromises.readFile(backupPath, 'utf8');
                resolve(content);
            } else {
                resolve('');
            }
        } catch (err) {
            reject(new Error(`Failed to read backup log file: ${err.message}`));
        }
    });
}

function copyLogToBackup(args) {
    return withLogLock(async () => {
        const logPath = getLogFileURL(args);
        const backupPath = getBackupLogFileURL(args);
        await fsPromises.copyFile(logPath, backupPath);
        return 'Log file copied to backup successfully';
    });
}

function loggerWithLevel(args) {
    return withLogLock(async () => {
        if (!args || !args[0]) throw new Error("Invalid arguments provided");
        const { userId, level, sourceClass, sourceMethod, message } = args[0];
        if (!userId) throw new Error("userId is required");
        if (!level) throw new Error("level is required");
        if (!message) throw new Error("message is required");

        // Normalize log levels for comparison
        const normalizedDefaultLogLevel = (defaultLogLevel || '').toLowerCase();
        const normalizedLevel = (level || '').toLowerCase();

        // Log level filtering using normalized values
        if (
            (normalizedDefaultLogLevel === LogLevel.Error && (normalizedLevel === LogLevel.Debug || normalizedLevel === LogLevel.Info)) ||
            (normalizedDefaultLogLevel === LogLevel.Info && normalizedLevel === LogLevel.Debug)
        ) {
            return;
        }

        await checkAndRotateLogFile(args);

        const logPath = getLogFileURL(args);
        const currentDate = new Date();
        const formatter = new Intl.DateTimeFormat('en-US', {
            day: '2-digit', month: '2-digit', year: 'numeric',
            hour: '2-digit', minute: '2-digit', second: '2-digit'
        });
        const localDateString = formatter.format(currentDate);

        const dateUtc = new Date(currentDate.toUTCString());
        const utcFormatter = new Intl.DateTimeFormat('en-US', {
            day: '2-digit', month: '2-digit', year: 'numeric',
            hour: '2-digit', minute: '2-digit', second: '2-digit', timeZone: 'UTC'
        });
        const utcDateString = utcFormatter.format(dateUtc);

        let data = `${localDateString} | UTC:${utcDateString} | ${getStringFromLevel(level)} | ${sourceClass} | ${sourceMethod} | ${message}\n`;
        if (level === LogLevel.Error) {
            data = `⭕️⭕️⭕️ ${data}`;
        }
        console.log(data);
        await fsPromises.appendFile(logPath, data, 'utf8');
        return `Logged to ${logPath}`;
    });
}

function getLogDirectory(args) {
    if (!args || !args[0] || !args[0].userId) {
        throw new Error("userId is required");
    }
    const userId = args[0].userId;
    const userDataPath = app.getPath('userData');
    const userPath = path.join(userDataPath, userId);
    if (!fs.existsSync(userPath)) {
        fs.mkdirSync(userPath, { recursive: true });
    }
    return userPath;
}

function getStringFromLevel(level) {
    switch (level) {
        case LogLevel.Info: return 'IMPORTANT';
        case LogLevel.Error: return 'ERROR';
        case LogLevel.Debug: return 'DEBUG';
        default: return '';
    }
}

function checkAndRotateLogFile(args) {
    return withLogLock(async () => {
        const logPath = getLogFileURL(args);
        if (fs.existsSync(logPath)) {
            const stats = await fsPromises.stat(logPath);
            if (stats.size > MAX_LOG_SIZE) {
                const backupPath = getBackupLogFileURL(args);
                await fsPromises.copyFile(logPath, backupPath);
                await fsPromises.writeFile(logPath, '');
                return 'Log file rotated due to size limit';
            }
        }
        return 'Log file rotation check completed';
    });
}

module.exports = {
    logDebug,
    logError,
    logInfo,
    setLogLevel,
    getLogLevel,
    getLogFileContent,
    clearLogFile,
    getBackupLogFileContent,
    LogLevel,
    copyLogToBackup,
    getLogFileURL,
    getBackupLogFileURL,
};